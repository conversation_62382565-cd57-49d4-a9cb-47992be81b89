# -*- coding: utf-8 -*-
"""
日志配置模块

提供按天分割的日志功能，支持不同级别的日志记录。
"""

import os
import logging
from logging.handlers import TimedRotatingFileHandler
from datetime import datetime
from typing import Optional


class DailyLogger:
    """
    按天分割的日志记录器

    功能:
    - 按天自动分割日志文件
    - 支持不同日志级别
    - 自动创建日志目录
    - 保留指定天数的日志文件
    """

    def __init__(self,
                 name: str = 'aihub',
                 log_dir: str = 'logs',
                 log_level: int = logging.DEBUG,
                 backup_count: int = 30,
                 encoding: str = 'utf-8'):
        """
        初始化日志记录器

        Args:
            name (str): 日志记录器名称
            log_dir (str): 日志文件目录
            log_level (int): 日志级别
            backup_count (int): 保留的日志文件数量（天数）
            encoding (str): 日志文件编码
        """
        self.name = name
        self.log_dir = log_dir
        self.log_level = log_level
        self.backup_count = backup_count
        self.encoding = encoding

        # 创建日志目录
        self._ensure_log_dir()

        # 初始化日志记录器
        self.logger = self._setup_logger()

    def _ensure_log_dir(self):
        """
        确保日志目录存在
        """
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir, exist_ok=True)

    def _setup_logger(self) -> logging.Logger:
        """
        设置日志记录器

        Returns:
            logging.Logger: 配置好的日志记录器
        """
        # 创建日志记录器
        logger = logging.getLogger(self.name)
        logger.setLevel(self.log_level)

        # 清除已有的处理器，避免重复
        logger.handlers.clear()

        # 创建日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # 文件处理器 - 按天分割
        log_file = os.path.join(self.log_dir, f'{self.name}.log')
        file_handler = TimedRotatingFileHandler(
            filename=log_file,
            when='midnight',  # 每天午夜分割
            interval=1,       # 间隔1天
            backupCount=self.backup_count,  # 保留文件数量
            encoding=self.encoding,
            utc=False  # 使用本地时间
        )
        file_handler.setLevel(self.log_level)
        file_handler.setFormatter(formatter)

        # 设置日志文件名后缀格式
        file_handler.suffix = '%Y-%m-%d'

        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(self.log_level)
        console_handler.setFormatter(formatter)

        # 添加处理器到日志记录器
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        return logger

    def get_logger(self) -> logging.Logger:
        """
        获取日志记录器实例

        Returns:
            logging.Logger: 日志记录器
        """
        return self.logger

    def info(self, message: str):
        """记录INFO级别日志"""
        self.logger.info(message)

    def debug(self, message: str):
        """记录DEBUG级别日志"""
        self.logger.debug(message)

    def warning(self, message: str):
        """记录WARNING级别日志"""
        self.logger.warning(message)

    def error(self, message: str):
        """记录ERROR级别日志"""
        self.logger.error(message)

    def critical(self, message: str):
        """记录CRITICAL级别日志"""
        self.logger.critical(message)

    def exception(self, message: str):
        """记录异常信息"""
        self.logger.exception(message)


# 全局日志记录器实例
_global_logger: Optional[DailyLogger] = None


def setup_logging(log_dir: str = 'logs',
                  log_level: int = logging.INFO,
                  backup_count: int = 30) -> DailyLogger:
    """
    设置全局日志配置

    Args:
        log_dir (str): 日志文件目录
        log_level (int): 日志级别
        backup_count (int): 保留的日志文件数量（天数）

    Returns:
        DailyLogger: 日志记录器实例
    """
    global _global_logger

    if _global_logger is None:
        _global_logger = DailyLogger(
            name='aihub',
            log_dir=log_dir,
            log_level=log_level,
            backup_count=backup_count
        )

    return _global_logger


def get_logger(name: Optional[str] = None) -> logging.Logger:
    """
    获取全局日志记录器

    Args:
        name (Optional[str]): 日志记录器名称，默认为None

    Returns:
        logging.Logger: 日志记录器
    """
    global _global_logger

    if _global_logger is None:
        _global_logger = setup_logging()

    return _global_logger.get_logger()


def log_request(method: str, url: str, data: dict = None, response_time: float = None):
    """
    记录HTTP请求日志

    Args:
        method (str): HTTP方法
        url (str): 请求URL
        data (dict): 请求数据
        response_time (float): 响应时间（秒）
    """
    logger = get_logger()

    log_msg = f"HTTP {method} {url}"
    if response_time is not None:
        log_msg += f" - {response_time:.3f}s"

    if data:
        log_msg += f" - Data: {data}"

    logger.info(log_msg)


def log_api_call(api_name: str, params: dict = None, result: dict = None, error: str = None):
    """
    记录API调用日志

    Args:
        api_name (str): API名称
        params (dict): 调用参数
        result (dict): 调用结果
        error (str): 错误信息
    """
    logger = get_logger()

    log_msg = f"API Call: {api_name}"

    if params:
        log_msg += f" - Params: {params}"

    if error:
        logger.error(f"{log_msg} - Error: {error}")
    elif result:
        logger.info(f"{log_msg} - Success: {result}")
    else:
        logger.info(log_msg)


def cleanup_old_logs(log_dir: str = 'logs', days_to_keep: int = 30):
    """
    清理旧的日志文件

    Args:
        log_dir (str): 日志目录
        days_to_keep (int): 保留天数
    """
    if not os.path.exists(log_dir):
        return

    logger = get_logger()
    current_time = datetime.now()

    try:
        for filename in os.listdir(log_dir):
            if filename.endswith('.log'):
                file_path = os.path.join(log_dir, filename)
                file_time = datetime.fromtimestamp(os.path.getctime(file_path))

                # 计算文件年龄
                age_days = (current_time - file_time).days

                if age_days > days_to_keep:
                    os.remove(file_path)
                    logger.info(f"删除旧日志文件: {filename} (年龄: {age_days}天)")

    except Exception as e:
        logger.error(f"清理日志文件时出错: {str(e)}")