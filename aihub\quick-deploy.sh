#!/bin/bash

# 千义听悟音频转写服务器 - 快速部署脚本
# 适用于Ubuntu/Debian/CentOS等Linux发行版
# 使用方法: curl -fsSL https://your-domain.com/quick-deploy.sh | sudo bash

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
APP_NAME="tingwu-server"
APP_DIR="/opt/${APP_NAME}"
SERVICE_NAME="tingwu-server"
PORT=5579
GITHUB_REPO="your-username/tingwu-server"  # 替换为实际的GitHub仓库
DOWNLOAD_URL="https://github.com/${GITHUB_REPO}/archive/main.zip"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    echo
    echo "======================================"
    echo "  千义听悟音频转写服务器 快速部署"
    echo "======================================"
    echo
    log_info "开始自动部署千义听悟音频转写服务器..."
    echo
}

# 检查root权限
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0 或 curl -fsSL <script-url> | sudo bash"
        exit 1
    fi
}

# 检测系统类型
detect_system() {
    log_info "检测系统类型..."
    
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
        log_info "检测到系统: $OS $VER"
        
        # 设置包管理器
        if command -v apt-get >/dev/null 2>&1; then
            PKG_MANAGER="apt"
            UPDATE_CMD="apt-get update"
            INSTALL_CMD="apt-get install -y"
        elif command -v yum >/dev/null 2>&1; then
            PKG_MANAGER="yum"
            UPDATE_CMD="yum update -y"
            INSTALL_CMD="yum install -y"
        elif command -v dnf >/dev/null 2>&1; then
            PKG_MANAGER="dnf"
            UPDATE_CMD="dnf update -y"
            INSTALL_CMD="dnf install -y"
        else
            log_error "不支持的包管理器"
            exit 1
        fi
    else
        log_error "无法检测系统类型"
        exit 1
    fi
}

# 安装系统依赖
install_dependencies() {
    log_info "更新系统包列表..."
    $UPDATE_CMD
    
    log_info "安装系统依赖..."
    if [[ $PKG_MANAGER == "apt" ]]; then
        $INSTALL_CMD python3 python3-pip python3-venv python3-dev \
                    build-essential curl wget unzip git systemd
    elif [[ $PKG_MANAGER == "yum" ]] || [[ $PKG_MANAGER == "dnf" ]]; then
        $INSTALL_CMD python3 python3-pip python3-devel gcc gcc-c++ \
                    curl wget unzip git systemd
    fi
    
    log_success "系统依赖安装完成"
}

# 创建应用用户
create_user() {
    log_info "创建应用用户..."
    
    if ! id "www-data" &>/dev/null; then
        useradd --system --shell /bin/false --home-dir $APP_DIR \
                --create-home --user-group www-data
        log_success "用户 www-data 创建成功"
    else
        log_info "用户 www-data 已存在"
    fi
}

# 下载应用代码
download_app() {
    log_info "下载应用代码..."
    
    # 创建临时目录
    TEMP_DIR=$(mktemp -d)
    cd "$TEMP_DIR"
    
    # 下载代码
    if command -v git >/dev/null 2>&1; then
        log_info "使用Git克隆仓库..."
        git clone "https://github.com/${GITHUB_REPO}.git" tingwu-server
        cd tingwu-server
    else
        log_info "下载ZIP包..."
        wget -O tingwu-server.zip "$DOWNLOAD_URL"
        unzip tingwu-server.zip
        cd tingwu-server-main
    fi
    
    # 创建应用目录
    mkdir -p $APP_DIR
    mkdir -p $APP_DIR/logs
    
    # 复制文件
    cp -r tingwu_server $APP_DIR/
    cp requirements.txt $APP_DIR/
    cp setup.py $APP_DIR/
    cp README.md $APP_DIR/
    cp -r deploy $APP_DIR/
    
    # 设置权限
    chown -R www-data:www-data $APP_DIR
    
    # 清理临时文件
    cd /
    rm -rf "$TEMP_DIR"
    
    log_success "应用代码下载完成"
}

# 安装Python依赖
install_python_deps() {
    log_info "创建Python虚拟环境..."
    
    cd $APP_DIR
    sudo -u www-data python3 -m venv venv
    
    log_info "安装Python依赖包..."
    sudo -u www-data bash -c "source venv/bin/activate && pip install --upgrade pip"
    sudo -u www-data bash -c "source venv/bin/activate && pip install -r requirements.txt"
    
    log_success "Python依赖安装完成"
}

# 创建配置文件
create_config() {
    log_info "创建配置文件..."
    
    # 复制环境变量模板
    cp $APP_DIR/deploy/.env.example $APP_DIR/.env
    
    # 设置基本配置
    cat >> $APP_DIR/.env << EOF

# 服务器配置
TINGWU_HOST=0.0.0.0
TINGWU_PORT=$PORT
TINGWU_THREADS=4
FLASK_ENV=production

# 日志配置
TINGWU_LOG_LEVEL=INFO
TINGWU_LOG_FILE=$APP_DIR/logs/tingwu-server.log
EOF
    
    chown www-data:www-data $APP_DIR/.env
    chmod 600 $APP_DIR/.env
    
    log_success "配置文件创建完成"
}

# 安装systemd服务
install_service() {
    log_info "安装systemd服务..."
    
    cp $APP_DIR/deploy/tingwu-server.service /etc/systemd/system/
    systemctl daemon-reload
    systemctl enable $SERVICE_NAME
    
    log_success "systemd服务安装完成"
}

# 配置防火墙
setup_firewall() {
    log_info "配置防火墙..."
    
    if command -v ufw >/dev/null 2>&1; then
        ufw allow $PORT/tcp
        log_success "UFW防火墙规则添加完成"
    elif command -v firewall-cmd >/dev/null 2>&1; then
        firewall-cmd --permanent --add-port=$PORT/tcp
        firewall-cmd --reload
        log_success "firewalld防火墙规则添加完成"
    else
        log_warning "未检测到防火墙，请手动开放端口 $PORT"
    fi
}

# 启动服务
start_service() {
    log_info "启动服务..."
    
    systemctl start $SERVICE_NAME
    sleep 3
    
    if systemctl is-active --quiet $SERVICE_NAME; then
        log_success "服务启动成功"
    else
        log_warning "服务启动可能失败，请检查配置"
        log_info "查看日志: journalctl -u $SERVICE_NAME -f"
    fi
}

# 测试服务
test_service() {
    log_info "测试服务..."
    
    sleep 5
    
    if curl -s "http://localhost:$PORT/health" >/dev/null; then
        log_success "服务测试通过"
        echo
        echo "健康检查响应:"
        curl -s "http://localhost:$PORT/health" | python3 -m json.tool 2>/dev/null || curl -s "http://localhost:$PORT/health"
    else
        log_warning "服务测试失败，可能需要配置阿里云凭证"
    fi
}

# 显示完成信息
show_completion() {
    echo
    log_success "部署完成！"
    echo
    echo "======================================"
    echo "           部署信息"
    echo "======================================"
    echo "应用目录: $APP_DIR"
    echo "服务名称: $SERVICE_NAME"
    echo "监听端口: $PORT"
    echo "配置文件: $APP_DIR/.env"
    echo "日志文件: $APP_DIR/logs/tingwu-server.log"
    echo
    echo "======================================"
    echo "           下一步操作"
    echo "======================================"
    echo "1. 配置阿里云凭证:"
    echo "   sudo nano $APP_DIR/.env"
    echo "   # 填入 ALIBABA_CLOUD_ACCESS_KEY_ID 和 ALIBABA_CLOUD_ACCESS_KEY_SECRET"
    echo
    echo "2. 重启服务使配置生效:"
    echo "   sudo systemctl restart $SERVICE_NAME"
    echo
    echo "3. 查看服务状态:"
    echo "   sudo systemctl status $SERVICE_NAME"
    echo
    echo "4. 测试API接口:"
    echo "   curl http://localhost:$PORT/health"
    echo
    echo "======================================"
    echo "           常用命令"
    echo "======================================"
    echo "启动服务: sudo systemctl start $SERVICE_NAME"
    echo "停止服务: sudo systemctl stop $SERVICE_NAME"
    echo "重启服务: sudo systemctl restart $SERVICE_NAME"
    echo "查看状态: sudo systemctl status $SERVICE_NAME"
    echo "查看日志: sudo journalctl -u $SERVICE_NAME -f"
    echo "管理脚本: $APP_DIR/deploy/manage.sh"
    echo
    echo "======================================"
    echo "           访问地址"
    echo "======================================"
    echo "健康检查: http://$(hostname -I | awk '{print $1}'):$PORT/health"
    echo "API接口:  http://$(hostname -I | awk '{print $1}'):$PORT/ai/aliyun/voice/tingwu/new"
    echo
    log_warning "重要提醒: 请务必配置阿里云凭证后重启服务！"
    echo
}

# 主函数
main() {
    show_welcome
    check_root
    detect_system
    install_dependencies
    create_user
    download_app
    install_python_deps
    create_config
    install_service
    setup_firewall
    start_service
    test_service
    show_completion
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 运行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi