# 创建听悟任务完整工程示例

该项目为CreateTask的完整工程示例。

该示例**无法在线调试**，如需调试可下载到本地后替换 [AK](https://usercenter.console.aliyun.com/#/manage/ak) 以及参数后进行调试。

## 运行条件

- 下载并解压需要语言的代码;


- 在阿里云帐户中获取您的 [凭证](https://usercenter.console.aliyun.com/#/manage/ak) 并通过它替换下载后代码中的 ACCESS_KEY_ID 以及 ACCESS_KEY_SECRET;

- 执行对应语言的构建及运行语句

## 执行步骤

下载的代码包，在根据自己需要更改代码中的参数和 AK 以后，可以在**解压代码所在目录下**按如下的步骤执行：

- *Python 版本要求 Python3*
```sh
python3 setup.py install && python ./alibabacloud_sample/sample.py
```
## 使用的 API

-  CreateTask：创建听悟任务，包括创建离线转写任务和实时会议任务。 更多信息可参考：[文档](https://next.api.aliyun.com/document/tingwu/2023-09-30/CreateTask)

## API 返回示例

*实际输出结构可能稍有不同，属于正常返回；下列输出值仅作为参考，以实际调用为准*


- JSON 格式 
```js
{
  "RequestId": "35124E1C-AE99-5D6C-A52E-BD689D8D****",
  "Code": "0",
  "Message": "Success.",
  "Data": {
    "TaskId": "c5394c6ee0fb474899d42215a3925c7e",
    "TaskKey": "task_tingwu_123",
    "MeetingJoinUrl": "wss://tingwu-realtime-cn-beijing.aliyuncs.com/api/ws/v1?mc=****",
    "TaskStatus": "ONGOING"
  }
}
```

