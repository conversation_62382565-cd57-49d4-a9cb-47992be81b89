#!/bin/bash

# AI Hub服务器 - 服务管理脚本
# 作者: Assistant
# 版本: 1.0.0

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SERVICE_NAME="aihub-server"
APP_DIR="/opt/aihub-server"
PORT=5579

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务状态
check_service_status() {
    if systemctl is-active --quiet $SERVICE_NAME; then
        return 0  # 运行中
    else
        return 1  # 未运行
    fi
}

# 启动服务
start_service() {
    log_info "启动 $SERVICE_NAME 服务..."
    
    if check_service_status; then
        log_warning "服务已经在运行中"
        return 0
    fi
    
    sudo systemctl start $SERVICE_NAME
    sleep 3
    
    if check_service_status; then
        log_success "服务启动成功"
        show_status
    else
        log_error "服务启动失败"
        log_info "查看详细日志: $0 logs"
        return 1
    fi
}

# 停止服务
stop_service() {
    log_info "停止 $SERVICE_NAME 服务..."
    
    if ! check_service_status; then
        log_warning "服务未在运行"
        return 0
    fi
    
    sudo systemctl stop $SERVICE_NAME
    sleep 2
    
    if ! check_service_status; then
        log_success "服务停止成功"
    else
        log_error "服务停止失败"
        return 1
    fi
}

# 重启服务
restart_service() {
    log_info "重启 $SERVICE_NAME 服务..."
    
    sudo systemctl restart $SERVICE_NAME
    sleep 3
    
    if check_service_status; then
        log_success "服务重启成功"
        show_status
    else
        log_error "服务重启失败"
        log_info "查看详细日志: $0 logs"
        return 1
    fi
}

# 重新加载服务
reload_service() {
    log_info "重新加载 $SERVICE_NAME 服务配置..."
    
    sudo systemctl daemon-reload
    sudo systemctl reload-or-restart $SERVICE_NAME
    sleep 3
    
    if check_service_status; then
        log_success "服务重新加载成功"
        show_status
    else
        log_error "服务重新加载失败"
        return 1
    fi
}

# 显示服务状态
show_status() {
    echo
    echo "=== 服务状态 ==="
    sudo systemctl status $SERVICE_NAME --no-pager -l
    
    echo
    echo "=== 端口监听 ==="
    if command -v ss >/dev/null 2>&1; then
        ss -tlnp | grep :$PORT || echo "端口 $PORT 未监听"
    elif command -v netstat >/dev/null 2>&1; then
        netstat -tlnp | grep :$PORT || echo "端口 $PORT 未监听"
    else
        log_warning "无法检查端口状态"
    fi
    
    echo
    echo "=== 健康检查 ==="
    if curl -s "http://localhost:$PORT/health" >/dev/null 2>&1; then
        log_success "健康检查通过"
        curl -s "http://localhost:$PORT/health" | python3 -m json.tool 2>/dev/null || echo "JSON解析失败"
    else
        log_error "健康检查失败"
    fi
}

# 查看日志
show_logs() {
    local lines=${1:-50}
    
    echo "=== 系统日志 (最近 $lines 行) ==="
    sudo journalctl -u $SERVICE_NAME -n $lines --no-pager
    
    echo
    echo "=== 应用日志 ==="
    if [[ -f "$APP_DIR/logs/tingwu-server.log" ]]; then
        tail -n $lines "$APP_DIR/logs/tingwu-server.log"
    else
        log_warning "应用日志文件不存在: $APP_DIR/logs/tingwu-server.log"
    fi
}

# 实时查看日志
follow_logs() {
    log_info "实时查看日志 (Ctrl+C 退出)..."
    
    # 同时查看系统日志和应用日志
    if [[ -f "$APP_DIR/logs/tingwu-server.log" ]]; then
        sudo journalctl -u $SERVICE_NAME -f &
        tail -f "$APP_DIR/logs/tingwu-server.log" &
        wait
    else
        sudo journalctl -u $SERVICE_NAME -f
    fi
}

# 测试API
test_api() {
    log_info "测试API接口..."
    
    echo
    echo "=== 健康检查 ==="
    if curl -s "http://localhost:$PORT/health"; then
        echo
        log_success "健康检查通过"
    else
        echo
        log_error "健康检查失败"
        return 1
    fi
    
    echo
    echo "=== API测试 ==="
    echo "测试创建转写任务接口..."
    
    # 测试API（使用示例数据）
    response=$(curl -s -X POST "http://localhost:$PORT/ai/aliyun/voice/tingwu/new" \
        -H "Content-Type: application/json" \
        -d '{
            "TaskKey": "test_task_'$(date +%s)'",
            "fileUrl": "http://example.com/test.mp3"
        }' 2>/dev/null)
    
    if [[ $? -eq 0 && -n "$response" ]]; then
        echo "$response" | python3 -m json.tool 2>/dev/null || echo "$response"
        log_success "API测试完成"
    else
        log_error "API测试失败"
        return 1
    fi
}

# 更新应用
update_app() {
    log_info "更新应用..."
    
    # 停止服务
    stop_service
    
    # 备份当前版本
    backup_dir="$APP_DIR/backup/$(date +%Y%m%d_%H%M%S)"
    sudo mkdir -p "$backup_dir"
    sudo cp -r "$APP_DIR/tingwu_server" "$backup_dir/" 2>/dev/null || true
    
    log_info "当前版本已备份到: $backup_dir"
    
    # 这里可以添加更新逻辑
    log_warning "请手动更新应用文件，然后运行: $0 start"
}

# 显示配置信息
show_config() {
    echo "=== 服务配置 ==="
    echo "服务名称: $SERVICE_NAME"
    echo "应用目录: $APP_DIR"
    echo "监听端口: $PORT"
    echo "配置文件: $APP_DIR/.env"
    echo "服务文件: /etc/systemd/system/$SERVICE_NAME.service"
    echo
    
    echo "=== 环境变量 ==="
    if [[ -f "$APP_DIR/.env" ]]; then
        grep -v '^#' "$APP_DIR/.env" | grep -v '^$' | while read line; do
            key=$(echo "$line" | cut -d'=' -f1)
            echo "$key=***"
        done
    else
        log_warning "环境变量文件不存在: $APP_DIR/.env"
    fi
}

# 显示帮助信息
show_help() {
    echo "千义听悟音频转写服务器 - 服务管理脚本"
    echo
    echo "用法: $0 <命令> [选项]"
    echo
    echo "命令:"
    echo "  start          启动服务"
    echo "  stop           停止服务"
    echo "  restart        重启服务"
    echo "  reload         重新加载配置"
    echo "  status         显示服务状态"
    echo "  logs [行数]    查看日志 (默认50行)"
    echo "  follow         实时查看日志"
    echo "  test           测试API接口"
    echo "  config         显示配置信息"
    echo "  update         更新应用"
    echo "  help           显示帮助信息"
    echo
    echo "示例:"
    echo "  $0 start                # 启动服务"
    echo "  $0 logs 100             # 查看最近100行日志"
    echo "  $0 test                 # 测试API"
}

# 主函数
main() {
    case "$1" in
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        reload)
            reload_service
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs "$2"
            ;;
        follow)
            follow_logs
            ;;
        test)
            test_api
            ;;
        config)
            show_config
            ;;
        update)
            update_app
            ;;
        help|--help|-h)
            show_help
            ;;
        "")
            log_error "请指定命令"
            echo
            show_help
            exit 1
            ;;
        *)
            log_error "未知命令: $1"
            echo
            show_help
            exit 1
            ;;
    esac
}

# 如果直接运行脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi