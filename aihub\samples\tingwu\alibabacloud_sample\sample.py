# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
import os
import sys

from typing import List

from alibabacloud_tingwu20230930.client import Client as tingwu20230930Client
from alibabacloud_credentials.client import Client as CredentialClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_tingwu20230930 import models as tingwu_20230930_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_console.client import Client as ConsoleClient
from alibabacloud_tea_util.client import Client as UtilClient


class Sample:
    def __init__(self):
        pass

    @staticmethod
    def create_client() -> tingwu20230930Client:
        """
        使用凭据初始化账号Client
        @return: Client
        @throws Exception
        """
        # 工程代码建议使用更安全的无AK方式，凭据配置方式请参见：https://help.aliyun.com/document_detail/378659.html。
        credential = CredentialClient()
        config = open_api_models.Config(
            access_key_id = 'LTAI5tLmDUUpo5iRK8DFaUs2',
            access_key_secret = '******************************'
        )
        # Endpoint 请参考 https://api.aliyun.com/product/tingwu
        config.endpoint = f'tingwu.cn-beijing.aliyuncs.com'
        return tingwu20230930Client(config)

    @staticmethod
    def main(
        args: List[str],
    ) -> None:
        client = Sample.create_client()
        parameters_custom_prompt_contents_0 = tingwu_20230930_models.CreateTaskRequestParametersCustomPromptContents(
            # String, 必填, Prompt的自定义名称，用于匹配输出结果。,
            name='concat-dialogs',
            # String, 必填, Prompt的自定义内容。,
            prompt='把对话内容连接成一整段文本:{Transcription}'
        )
        parameters_custom_prompt = tingwu_20230930_models.CreateTaskRequestParametersCustomPrompt(
            # Array, 可选,
            contents=[
                parameters_custom_prompt_contents_0
            ]
        )
        parameters_transcription_diarization = tingwu_20230930_models.CreateTaskRequestParametersTranscriptionDiarization(
            # Integer, 可选, 设置说话人分离参数。  不设置：不使用说话人角色区分。   0：说话人角色区分结果为不定人数。   2：说话人角色区分结果为2人。,
            speaker_count=0
        )
        parameters_transcription = tingwu_20230930_models.CreateTaskRequestParametersTranscription(
            # Boolean, 可选, 是否开启说话人分离功能,
            diarization_enabled=True,
            # Object, 可选,
            diarization=parameters_transcription_diarization
        )
        parameters = tingwu_20230930_models.CreateTaskRequestParameters(
            # Object, 可选,
            transcription=parameters_transcription,
            # Boolean, 可选, 是否启用自定义Prompt功能。,
            custom_prompt_enabled=True,
            # Object, 可选,
            custom_prompt=parameters_custom_prompt
        )
        input = tingwu_20230930_models.CreateTaskRequestInput(
            # String, 必填, 音频转写使用的语言模型。 支持以下取值：  - **cn**：中文 - **en**：英文 - **fspk**：中英文自由说 - **ja**：日文 - **yue**：粤语,
            source_language='cn',
            # String, 可选, 当您创建离线转写任务，设置的原始音视频文件的http(s)链接,
            file_url='http://lcmp.zixushuili.com/assets/voice.m4a',
            # String, 可选, 用户设置的自定义标识，用以关联本任务。,
            task_key='task_tingwu_123',
            # Integer, 可选, 您创建实时会议时，需通过该参数指定音频流数据的采样率。当前支持8000和16000。  - **8000**：电话客服类场景 - **16000**：实时会议音频采集场景,
            sample_rate=16000,
            # Boolean, 可选, 是否开启回调功能。 当需要开启回调功能时，您需要在控制台配置好回调类型和地址，并在创建任务时将该参数置为true。,
            progressive_callbacks_enabled=True
        )
        create_task_request = tingwu_20230930_models.CreateTaskRequest(
            type='offline',
            # String, 可选, 在管控台创建的项目AppKey。,
            app_key='VsqGahYCPigFgKEC',
            # Object, 可选,
            input=input,
            # Object, 可选,
            parameters=parameters
        )
        runtime = util_models.RuntimeOptions()
        headers = {}
        try:
            resp = client.create_task_with_options(create_task_request, headers, runtime)
            ConsoleClient.log(UtilClient.to_jsonstring(resp))
        except Exception as error:
            # 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            # 错误 message
            print(error.message)
            # 诊断地址
            print(error.data.get("Recommend"))
            UtilClient.assert_as_string(error.message)

    @staticmethod
    async def main_async(
        args: List[str],
    ) -> None:
        client = Sample.create_client()
        parameters_custom_prompt_contents_0 = tingwu_20230930_models.CreateTaskRequestParametersCustomPromptContents(
            # String, 必填, Prompt的自定义名称，用于匹配输出结果。,
            name='concat-dialogs',
            # String, 必填, Prompt的自定义内容。,
            prompt='把对话内容连接成一整段文本:{Transcription}'
        )
        parameters_custom_prompt = tingwu_20230930_models.CreateTaskRequestParametersCustomPrompt(
            # Array, 可选,
            contents=[
                parameters_custom_prompt_contents_0
            ]
        )
        parameters_transcription_diarization = tingwu_20230930_models.CreateTaskRequestParametersTranscriptionDiarization(
            # Integer, 可选, 设置说话人分离参数。  不设置：不使用说话人角色区分。   0：说话人角色区分结果为不定人数。   2：说话人角色区分结果为2人。,
            speaker_count=0
        )
        parameters_transcription = tingwu_20230930_models.CreateTaskRequestParametersTranscription(
            # Boolean, 可选, 是否开启说话人分离功能,
            diarization_enabled=True,
            # Object, 可选,
            diarization=parameters_transcription_diarization
        )
        parameters = tingwu_20230930_models.CreateTaskRequestParameters(
            # Object, 可选,
            transcription=parameters_transcription,
            # Boolean, 可选, 是否启用自定义Prompt功能。,
            custom_prompt_enabled=True,
            # Object, 可选,
            custom_prompt=parameters_custom_prompt
        )
        input = tingwu_20230930_models.CreateTaskRequestInput(
            # String, 必填, 音频转写使用的语言模型。 支持以下取值：  - **cn**：中文 - **en**：英文 - **fspk**：中英文自由说 - **ja**：日文 - **yue**：粤语,
            source_language='cn',
            # String, 可选, 当您创建离线转写任务，设置的原始音视频文件的http(s)链接,
            file_url='http://lcmp.zixushuili.com/assets/voice.m4a',
            # String, 可选, 用户设置的自定义标识，用以关联本任务。,
            task_key='task_tingwu_123',
            # Integer, 可选, 您创建实时会议时，需通过该参数指定音频流数据的采样率。当前支持8000和16000。  - **8000**：电话客服类场景 - **16000**：实时会议音频采集场景,
            sample_rate=16000,
            # Boolean, 可选, 是否开启回调功能。 当需要开启回调功能时，您需要在控制台配置好回调类型和地址，并在创建任务时将该参数置为true。,
            progressive_callbacks_enabled=True
        )
        create_task_request = tingwu_20230930_models.CreateTaskRequest(
            type='offline',
            # String, 可选, 在管控台创建的项目AppKey。,
            app_key='VsqGahYCPigFgKEC',
            # Object, 可选,
            input=input,
            # Object, 可选,
            parameters=parameters
        )
        runtime = util_models.RuntimeOptions()
        headers = {}
        try:
            resp = await client.create_task_with_options_async(create_task_request, headers, runtime)
            ConsoleClient.log(UtilClient.to_jsonstring(resp))
        except Exception as error:
            # 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            # 错误 message
            print(error.message)
            # 诊断地址
            print(error.data.get("Recommend"))
            UtilClient.assert_as_string(error.message)


if __name__ == '__main__':
    Sample.main(sys.argv[1:])
