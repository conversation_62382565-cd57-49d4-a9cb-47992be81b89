# -*- coding: utf-8 -*-
"""
千义听悟音频转写HTTP服务器主应用

基于Flask框架的HTTP服务器，提供音频文件离线转写服务。
"""

import json
import os
import sys
import time
import importlib.util
from pathlib import Path
from typing import Dict, Any
from flask import Flask, request, jsonify
from werkzeug.exceptions import BadRequest

from .tingwu import TingwuClient
from .utils import validate_request_data, create_error_response, create_success_response
from .logger import get_logger, log_request, log_api_call
from .config_manager import get_config_manager
from .chat import init_chat_client, handle_chat_request

# 获取环境配置
config_manager = get_config_manager()  # 自动从环境变量或默认值获取环境
environment = config_manager.environment

# 只保留日志记录器获取
logger = get_logger()

# 记录配置信息
config_info = config_manager.get_config_info()
logger.info(f"应用启动，环境: {environment}, 配置来源: {config_info['config_sources']}")
if config_info.get('config_file_path'):
    logger.info(f"配置文件: {config_info['config_file_path']}")


def check_python_version():
    """
    检查Python版本
    """
    if sys.version_info < (3, 10):
        logger.error("错误: 需要Python 3.10或更高版本")
        logger.error(f"当前版本: {sys.version}")
        return False
    else:
        logger.info(f"Python版本检查通过: {sys.version.split()[0]}")
        return True


def check_dependencies():
    """
    检查依赖包是否安装
    """
    required_packages = [
        'flask',
        'waitress',
        'alibabacloud_tea_openapi',
        'alibabacloud_credentials'
    ]

    missing_packages = []

    for package in required_packages:
        if importlib.util.find_spec(package) is None:
            missing_packages.append(package)

    if missing_packages:
        logger.error(f"缺少依赖包: {', '.join(missing_packages)}")
        logger.error("请运行以下命令安装依赖:")
        logger.error("pip install -r requirements.txt")
        return False
    else:
        logger.info("依赖包检查通过")
        return True


def check_credentials():
    """
    检查阿里云凭证配置
    """
    # 通过配置管理器获取凭证
    credentials = config_manager.get_aliyun_credentials()
    access_key_id = credentials.get('access_key_id')
    access_key_secret = credentials.get('access_key_secret')

    if access_key_id and access_key_secret:
        logger.info("发现阿里云凭证配置")
        return True

    logger.warning("警告: 未发现阿里云凭证配置")
    logger.warning("请到配置文件中添加以下凭证信息:")
    logger.warning("  ALIBABA_CLOUD_ACCESS_KEY_ID=your_access_key_id")
    logger.warning("  ALIBABA_CLOUD_ACCESS_KEY_SECRET=your_access_key_secret")
    return False


# 创建Flask应用
app = Flask(__name__)

# 初始化千义听悟客户端（传入环境配置）
tingwu_client = TingwuClient(environment=environment)

# 初始化大模型文本交互客户端
init_chat_client(environment=environment)


@app.route('/health', methods=['GET'])
def health_check():
    """
    健康检查端点

    Returns:
        JSON响应: 服务状态信息
    """
    start_time = time.time()

    try:
        # 记录健康检查请求
        log_request('GET', '/health')

        response_data = {
            "status": "healthy",
            "service": "aihub server",
            "version": "1.0.0",
            "timestamp": time.strftime('%Y-%m-%d %H:%M:%S')
        }

        response_time = time.time() - start_time
        log_request('GET', '/health', response_time=response_time)

        return jsonify(response_data)

    except Exception as e:
        response_time = time.time() - start_time
        error_msg = f"健康检查失败: {str(e)}"
        logger.error(f"{error_msg}, 耗时: {response_time:.3f}s")
        log_request('GET', '/health', response_time=response_time)
        return create_error_response(error_msg, 500)


@app.route('/ai/aliyun/voice/tingwu/new', methods=['POST'])
def create_transcription_task():
    """
    创建音频转写任务端点

    接收POST请求，创建千义听悟音频转写任务。

    请求体格式:
    {
        "TaskKey": "task_tingwu_123",
        "fileUrl": "http://example.com/audio.mp3",
        "sourceLanguage": "cn",  // 可选，默认为"cn"
        "sampleRate": 16000,  // 可选，默认为16000
        "speakerCount": 0,  // 可选，默认为0（自动检测）
        "outputLevel": 1,  // 可选，默认为1
        "diarizationEnabled": true,  // 可选，默认为true
        "progressiveCallbacksEnabled": true,  // 可选，默认为true
        "translationEnabled": false,  // 可选，默认为false
        "autoChaptersEnabled": false,  // 可选，默认为false
        "meetingAssistanceEnabled": false,  // 可选，默认为false
        "summarizationEnabled": false  // 可选，默认为false
    }

    Returns:
        JSON响应: 任务创建结果
    """
    start_time = time.time()

    try:
        # 记录请求日志
        log_request('POST', '/ai/aliyun/voice/tingwu/new')

        # 获取请求数据
        if not request.is_json:
            logger.error("请求Content-Type不是application/json")
            return create_error_response("请求Content-Type必须是application/json", 400)

        data = request.get_json()
        if not data:
            logger.error("请求体为空")
            return create_error_response("请求体不能为空", 400)

        # 记录请求数据（隐藏敏感信息）
        safe_data = {k: v for k, v in data.items() if k != 'fileUrl'}
        safe_data['fileUrl'] = '***' if 'fileUrl' in data else None
        logger.info(f"收到创建转写任务请求: {json.dumps(safe_data, ensure_ascii=False)}")

        # 验证必需参数
        validation_error = validate_request_data(data)
        if validation_error:
            logger.error(f"请求参数验证失败: {validation_error}")
            return create_error_response(validation_error, 400)

        # 提取参数
        task_key = data['TaskKey']
        file_url = data['fileUrl']

        # 可选参数，使用默认值
        source_language = data.get('sourceLanguage', 'cn')
        sample_rate = data.get('sampleRate', 16000)
        speaker_count = data.get('speakerCount', 0)
        output_level = data.get('outputLevel', 1)
        diarization_enabled = data.get('diarizationEnabled', True)
        progressive_callbacks_enabled = data.get('progressiveCallbacksEnabled', True)
        translation_enabled = data.get('translationEnabled', False)
        auto_chapters_enabled = data.get('autoChaptersEnabled', False)
        meeting_assistance_enabled = data.get('meetingAssistanceEnabled', False)
        summarization_enabled = data.get('summarizationEnabled', False)

        # 获取自定义prompt参数
        custom_prompt = data.get('Prompts', None)

        logger.info(f"开始创建转写任务，TaskKey: {task_key}")

        # 记录API调用
        api_params = {
            'task_key': task_key,
            'source_language': source_language,
            'sample_rate': sample_rate,
            'speaker_count': speaker_count,
            'output_level': output_level
        }

        # 调用千义听悟API创建任务
        result = tingwu_client.create_transcription_task(
            task_key=task_key,
            file_url=file_url,
            source_language=source_language,
            sample_rate=sample_rate,
            speaker_count=speaker_count,
            output_level=output_level,
            diarization_enabled=diarization_enabled,
            progressive_callbacks_enabled=progressive_callbacks_enabled,
            translation_enabled=translation_enabled,
            auto_chapters_enabled=auto_chapters_enabled,
            meeting_assistance_enabled=meeting_assistance_enabled,
            summarization_enabled=summarization_enabled,
            custom_prompt_contents=custom_prompt
        )

        # 记录成功的API调用
        response_time = time.time() - start_time
        log_api_call('create_transcription_task', api_params, {'task_key': task_key, 'status': 'success'})
        log_request('POST', '/ai/aliyun/voice/tingwu/new', response_time=response_time)

        logger.info(f"转写任务创建成功，TaskKey: {task_key}, 耗时: {response_time:.3f}s")

        # 返回API响应结果
        return jsonify(result)

    except Exception as e:
        error_msg = f"创建转写任务失败: {str(e)}"
        response_time = time.time() - start_time

        # 记录失败的API调用
        if 'task_key' in locals():
            log_api_call('create_transcription_task', api_params if 'api_params' in locals() else {}, error=error_msg)

        log_request('POST', '/ai/aliyun/voice/tingwu/new', response_time=response_time)
        logger.error(f"{error_msg}, 耗时: {response_time:.3f}s")
        return create_error_response(error_msg, 500)


@app.errorhandler(404)
def not_found(error):
    """
    404错误处理器

    Args:
        error: 错误对象

    Returns:
        JSON响应: 404错误信息
    """
    return create_error_response("接口不存在", 404)


@app.errorhandler(405)
def method_not_allowed(error):
    """
    405错误处理器

    Args:
        error: 错误对象

    Returns:
        JSON响应: 405错误信息
    """
    return create_error_response("请求方法不被允许", 405)


@app.errorhandler(500)
def internal_error(error):
    """
    500错误处理器

    Args:
        error: 错误对象

    Returns:
        JSON响应: 500错误信息
    """
    logger.error(f"服务器内部错误: {str(error)}")
    return create_error_response("服务器内部错误", 500)


@app.route('/ai/aliyun/chat/new', methods=['POST'])
def create_chat_task():
    """
    创建大模型文本交互任务端点

    接收POST请求，创建大模型文本交互任务。

    请求体格式:
    {
        "Model": "qwen-plus",  // 可选，默认为"qwen-plus"
        "SysContent": "你是一个AI助手",  // 系统角色的内容
        "UserContent": "你好，请介绍一下自己"  // 用户角色的内容
    }

    Returns:
        JSON响应: 大模型响应结果
    """
    return handle_chat_request()


def create_app():
    """
    创建Flask应用实例

    Returns:
        Flask: 配置好的Flask应用实例
    """
    return app
