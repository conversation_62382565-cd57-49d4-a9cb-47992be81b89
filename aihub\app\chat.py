﻿import time
import configparser
from pathlib import Path
from typing import Dict, Any
from http import HTTPStatus

import dashscope
from flask import request, jsonify, Response

from .logger import get_logger
from .utils import create_error_response


def log_api_call(api_name: str, params: dict = None, result: dict = None, error: str = None):
    """记录API调用日志

    Args:
        api_name (str): API名称
        params (dict): 调用参数
        result (dict): 调用结果
        error (str): 错误信息
    """
    logger = get_logger()

    log_msg = f"API Call: {api_name}"

    if params:
        log_msg += f" - Params: {params}"

    if error:
        logger.error(f"{log_msg} - Error: {error}")
    elif result:
        logger.info(f"{log_msg} - Success: {result}")
    else:
        logger.info(log_msg)


def log_request(method: str, url: str, data: dict = None, response_time: float = None):
    """记录HTTP请求日志

    Args:
        method (str): HTTP方法
        url (str): 请求URL
        data (dict): 请求数据
        response_time (float): 响应时间（秒）
    """
    logger = get_logger()

    log_msg = f"HTTP {method} {url}"
    if response_time is not None:
        log_msg += f" - {response_time:.3f}s"

    if data:
        log_msg += f" - Data: {data}"

    logger.info(log_msg)


class ChatClient:
    """大模型文本交互客户端

    用于处理与阿里云大模型的文本交互任务，包括创建聊天完成任务等功能。
    """

    def __init__(self, environment='default'):
        """初始化大模型文本交互客户端

        Args:
            environment: 环境名称，默认为'default'
        """
        self.environment = environment
        self.logger = get_logger()
        self.logger.info(f"初始化大模型文本交互客户端，环境: {environment}")

        # 加载API密钥
        self._load_api_key()

    def _load_api_key(self):
        """从配置文件加载API密钥

        优先从~/.alibabacloud/chat.ini加载，如果不存在则从config/chat.ini加载
        """
        # 获取配置文件路径
        home_dir = Path.home()
        alibabacloud_config = home_dir / '.alibabacloud' / 'chat.ini'
        project_config = Path('./config/chat.ini')

        config = configparser.ConfigParser()
        api_key = None
        config_path = None

        # 尝试从~/.alibabacloud/chat.ini加载
        if alibabacloud_config.exists() and alibabacloud_config.is_file():
            try:
                config.read(alibabacloud_config, encoding='utf-8')
                config_path = alibabacloud_config
                self.logger.info(f"从{alibabacloud_config}加载API密钥")
            except Exception as e:
                self.logger.warning(f"加载配置文件失败 {alibabacloud_config}: {e}")

        # 如果未找到配置文件，尝试从项目配置加载
        if (not config_path and
                project_config.exists() and
                project_config.is_file()):
            try:
                config.read(project_config, encoding='utf-8')
                config_path = project_config
                self.logger.info(f"从{project_config}加载API密钥")
            except Exception as e:
                self.logger.warning(f"加载配置文件失败 {project_config}: {e}")

        # 如果找到配置文件，从对应环境节获取API密钥
        if config_path:
            section = self.environment
            # 如果指定环境不存在，使用default环境
            if not config.has_section(section):
                section = 'default'
                self.logger.warning(f"环境{self.environment}不存在，使用default环境")

            # 如果default环境也不存在，使用第一个环境
            if not config.has_section(section) and config.sections():
                section = config.sections()[0]
                self.logger.warning(f"default环境不存在，使用{section}环境")

            # 获取API密钥
            if config.has_section(section):
                api_key = config.get(section, 'app_key', fallback=None)
                if api_key:
                    # 设置API密钥到dashscope
                    dashscope.api_key = api_key
                    # 打印掩码后的API密钥
                    masked_api_key = (api_key[:4] + '****' + api_key[-4:]
                                      if len(api_key) > 8 else '****')
                    self.logger.info(f"成功设置API密钥: {masked_api_key}")
                else:
                    self.logger.warning(f"在{section}环境中未找到app_key配置")
            else:
                self.logger.warning("未找到有效的配置环境")
        else:
            self.logger.warning(
                "未找到配置文件，请确保~/.alibabacloud/chat.ini或"
                "config/chat.ini存在")

        # 检查是否设置了API密钥
        if not dashscope.api_key:
            self.logger.warning("未设置API密钥，API调用可能会失败")

    def create_chat_completion(self, model: str, system_content: str,
                               user_content: str) -> Dict[str, Any]:
        """创建大模型文本交互任务

        Args:
            model: 使用的大模型名称
            system_content: 系统角色的内容
            user_content: 用户角色的内容

        Returns:
            Dict[str, Any]: 包含任务结果的字典
        """
        self.logger.info(f"创建大模型文本交互任务，模型: {model}")

        # 构建请求消息
        messages = [
            {
                "role": "system",
                "content": system_content
            },
            {
                "role": "user",
                "content": user_content
            }
        ]

        try:
            # 调用大模型API
            responses = dashscope.Generation.call(
                model=model,
                messages=messages,
                stream=True,  # 启用流式响应
                result_format='message',  # 指定结果格式为消息
                top_p=0.0001,
                temperature=0.7,
                enable_search=False,
                enable_thinking=False,
                thinking_budget=10000
            )

            # 处理响应结果
            for response in responses:
                if response.status_code == HTTPStatus.OK:
                    # 检查是否完成
                    if ('output' in response and
                            'choices' in response['output']):
                        for choice in response['output']['choices']:
                            if choice.get('finish_reason') == 'stop':
                                # 提取完成的消息内容
                                msg = choice.get('message', {})
                                content = msg.get('content', '')
                                self.logger.info(f"大模型Chat结果: {content}")
                                return {
                                    "status": "success",
                                    "content": content,
                                    "request_id": response.get(
                                        'request_id', '')
                                }
                else:
                    error_msg = (
                        f"大模型API调用失败: 请求ID: {response.request_id}, "
                        f"状态码: {response.status_code}, "
                        f"错误码: {response.code}, "
                        f"错误信息: {response.message}"
                    )
                    self.logger.error(error_msg)
                    return {
                        "status": "error",
                        "error": error_msg,
                        "request_id": response.request_id
                    }

            # 如果没有找到完成的响应
            return {
                "status": "error",
                "error": "未收到大模型完成的响应",
                "request_id": ""
            }

        except Exception as e:
            error_msg = f"调用大模型API失败: {str(e)}"
            self.logger.error(error_msg)
            return {
                "status": "error",
                "error": error_msg,
                "request_id": ""
            }


# 全局客户端实例
_chat_client = None


def init_chat_client(environment='default'):
    """初始化全局大模型文本交互客户端

    Args:
        environment: 环境名称，默认为'default'

    Returns:
        ChatClient: 大模型文本交互客户端实例
    """
    global _chat_client
    if _chat_client is None:
        _chat_client = ChatClient(environment=environment)
    return _chat_client


def get_chat_client():
    """获取全局大模型文本交互客户端实例

    如果客户端未初始化，则使用默认环境初始化

    Returns:
        ChatClient: 大模型文本交互客户端实例
    """
    global _chat_client
    if _chat_client is None:
        _chat_client = ChatClient()
    return _chat_client


def handle_chat_request():
    """处理大模型文本交互请求

    处理POST请求到/ai/aliyun/chat/new端点，提取请求参数，
    调用大模型创建文本交互任务，并返回结果。

    Returns:
        Response: Flask响应对象，包含JSON格式的结果
    """
    # 记录请求开始时间
    start_time = time.time()

    try:
        # 从请求体中获取参数
        data = request.get_json()
        if not data:
            return create_error_response("请求体不能为空", 400)

        # 提取参数
        model = data.get('Model', 'qwen-plus')  # 默认使用qwen-plus模型
        system_content = data.get('SysContent', '')
        user_content = data.get('UserContent', '')

        # 验证必要参数
        if not user_content:
            return create_error_response("UserContent不能为空", 400)

        # 获取客户端实例
        client = get_chat_client()

        # 调用大模型创建文本交互任务
        api_params = {
            "model": model,
            "system_content": system_content,
            "user_content": user_content
        }

        result = client.create_chat_completion(
            model=model,
            system_content=system_content,
            user_content=user_content
        )

        # 计算响应时间
        response_time = time.time() - start_time

        # 记录API调用和请求
        log_api_call(
            'create_chat_completion',
            api_params,
            {'status': result['status'],
             'request_id': result.get('request_id', '')}
        )
        log_request('POST', '/ai/aliyun/chat/new',
                    response_time=response_time)

        # 处理响应结果
        logger = get_logger()
        if result['status'] == 'success':
            logger.info(f"大模型文本交互成功，耗时: {response_time:.3f}s")

            # 获取原始内容
            raw_content = result['content']
            logger.info(f"[DEBUG] 原始响应内容: {raw_content}")

            # 解码Unicode字符串
            try:
                # 直接进行解码
                import json
                decoded_content = json.loads('"' + raw_content + '"')
                logger.info(f"[DEBUG] JSON解码后内容: {decoded_content}")
            except Exception as decode_error:
                logger.warning(f"Unicode解码失败，使用原始内容: {decode_error}")
                decoded_content = raw_content
                logger.info(f"[DEBUG] 使用原始内容: {decoded_content}")

            # 构建响应对象
            response_obj = {
                "status": "success",
                "content": decoded_content,
                "request_id": result.get('request_id', ''),
                "response_time": f"{response_time:.3f}s"
            }
            logger.info(f"[DEBUG] 最终响应对象: {json.dumps(response_obj, ensure_ascii=False)}")

            # 使用Response替代jsonify，并设置正确的Content-Type
            final_response = Response(
                json.dumps(response_obj, ensure_ascii=False),
                content_type='application/json; charset=utf-8'
            )
            logger.info(f"[DEBUG] 最终HTTP响应数据: {final_response.get_data(as_text=True)}")

            return final_response
        else:
            logger.error(
                f"大模型文本交互失败: {result.get('error', '未知错误')}, "
                f"耗时: {response_time:.3f}s"
            )
            return create_error_response(
                result.get('error', '大模型文本交互失败'), 500
            )

    except Exception as e:
        error_msg = f"处理大模型文本交互请求失败: {str(e)}"
        response_time = time.time() - start_time
        log_request('POST', '/ai/aliyun/chat/new',
                    response_time=response_time)
        logger = get_logger()
        logger.error(f"{error_msg}, 耗时: {response_time:.3f}s")
        return create_error_response(error_msg, 500)
