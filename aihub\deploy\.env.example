# AI Hub服务器 - 环境变量配置文件
# 复制此文件为 .env 并填入实际配置值

# ================================
# 环境配置
# ================================
# 运行环境 (可选值: default, development, production)
AIHUB_ENVIRONMENT=production

# ================================
# 阿里云凭证配置 (必填)
# ================================
# 阿里云访问密钥ID
ALIBABA_CLOUD_ACCESS_KEY_ID=your_access_key_id_here

# 阿里云访问密钥Secret
ALIBABA_CLOUD_ACCESS_KEY_SECRET=your_access_key_secret_here

# 阿里云地域ID (可选，默认: cn-beijing)
ALIBABA_CLOUD_REGION_ID=cn-beijing

# 千义听悟项目的AppKey (必填)
TINGWU_APP_KEY=your_app_key_here

# ================================
# 服务器配置
# ================================
# 服务器监听地址 (默认: 0.0.0.0)
AIHUB_HOST=0.0.0.0

# 服务器监听端口 (默认: 5579)
AIHUB_PORT=5579

# 工作线程数 (默认: 4)
AIHUB_THREADS=4

# 连接限制数 (默认: 100)
AIHUB_CONNECTION_LIMIT=100

# 清理间隔 (秒，默认: 30)
AIHUB_CLEANUP_INTERVAL=30

# 通道超时时间 (秒，默认: 120)
AIHUB_CHANNEL_TIMEOUT=120

# Flask运行环境 (development/production)
FLASK_ENV=production

# ================================
# 日志配置
# ================================
# 日志级别 (DEBUG/INFO/WARNING/ERROR)
LOG_LEVEL=INFO

# 日志目录
LOG_DIR=logs

# 日志文件保留数量
LOG_BACKUP_COUNT=30

# ================================
# 千义听悟API配置
# ================================
# API版本 (默认: 2023-09-30)
TINGWU_API_VERSION=2023-09-30

# API端点 (默认: tingwu.cn-beijing.aliyuncs.com)
TINGWU_API_ENDPOINT=tingwu.cn-beijing.aliyuncs.com

# 请求超时时间 (秒)
AIHUB_REQUEST_TIMEOUT=30

# 重试次数
AIHUB_RETRY_COUNT=3

# ================================
# 安全配置
# ================================
# API密钥 (可选，用于接口认证)
# AIHUB_API_KEY=your_api_key_here

# 允许的来源IP (逗号分隔，可选)
# AIHUB_ALLOWED_IPS=127.0.0.1,***********/24

# CORS允许的域名 (逗号分隔，可选)
# AIHUB_CORS_ORIGINS=https://example.com,https://app.example.com

# ================================
# 性能配置
# ================================
# 最大并发请求数
AIHUB_MAX_CONCURRENT_REQUESTS=10

# 请求队列大小
AIHUB_REQUEST_QUEUE_SIZE=100

# 连接池大小
AIHUB_CONNECTION_POOL_SIZE=20

# ================================
# 监控配置
# ================================
# 是否启用健康检查 (true/false)
TINGWU_HEALTH_CHECK_ENABLED=true

# 健康检查间隔 (秒)
TINGWU_HEALTH_CHECK_INTERVAL=30

# 是否启用指标收集 (true/false)
TINGWU_METRICS_ENABLED=false

# 指标端口 (如果启用指标收集)
TINGWU_METRICS_PORT=9090

# ================================
# 缓存配置 (可选)
# ================================
# Redis连接字符串 (可选，用于缓存)
# REDIS_URL=redis://localhost:6379/0

# 缓存过期时间 (秒)
# CACHE_EXPIRE_TIME=3600

# ================================
# 数据库配置 (可选)
# ================================
# 数据库连接字符串 (可选，用于存储任务信息)
# DATABASE_URL=sqlite:///tingwu.db
# DATABASE_URL=postgresql://user:password@localhost/tingwu
# DATABASE_URL=mysql://user:password@localhost/tingwu

# ================================
# 文件存储配置 (可选)
# ================================
# 本地文件存储路径
# TINGWU_UPLOAD_PATH=/opt/tingwu-server/uploads

# 最大文件大小 (MB)
# TINGWU_MAX_FILE_SIZE=100

# 允许的文件类型 (逗号分隔)
# TINGWU_ALLOWED_FILE_TYPES=mp3,wav,m4a,flac,aac

# ================================
# 第三方服务配置 (可选)
# ================================
# 对象存储配置 (阿里云OSS)
# OSS_ACCESS_KEY_ID=your_oss_access_key_id
# OSS_ACCESS_KEY_SECRET=your_oss_access_key_secret
# OSS_BUCKET_NAME=your_bucket_name
# OSS_ENDPOINT=oss-cn-beijing.aliyuncs.com

# 消息队列配置 (可选)
# MQ_URL=amqp://guest:guest@localhost:5672/

# ================================
# 开发配置 (仅开发环境)
# ================================
# 是否启用调试模式 (true/false)
# DEBUG=false

# 是否启用热重载 (true/false)
# HOT_RELOAD=false

# 是否启用详细日志 (true/false)
# VERBOSE_LOGGING=false