# -*- coding: utf-8 -*-
"""
AI Hub服务器启动脚本

用于生产环境启动HTTP服务器。
"""

import os
import sys
from waitress import serve
from .app import create_app, check_python_version, check_dependencies, check_credentials
from .logger import setup_logging, get_logger, cleanup_old_logs
from .config_manager import get_config_manager

# 获取环境配置
config_manager = get_config_manager()  # 自动从环境变量或默认值获取环境
environment = config_manager.environment

# 获取日志配置
logging_config = config_manager.get_logging_config()

# 初始化日志系统
setup_logging(
    log_dir=logging_config.get('log_dir', 'logs'),
    log_level=logging_config.get('log_level', 20),
    backup_count=logging_config.get('backup_count', 30)
)
logger = get_logger()

# 清理旧日志文件
cleanup_old_logs(logging_config.get('log_dir', 'logs'), logging_config.get('backup_count', 30))

# 记录配置信息
config_info = config_manager.get_config_info()
logger.info(f"服务器启动，环境: {environment}, 配置来源: {config_info['config_sources']}")
if config_info.get('config_file_path'):
    logger.info(f"配置文件: {config_info['config_file_path']}")


# 检查函数已在导入语句中引入


def main():
    """
    启动生产服务器主函数
    """
    # 环境检查
    if not check_python_version():
        sys.exit(1)

    if not check_dependencies():
        sys.exit(1)

    # 检查凭证（仅警告，不退出）
    check_credentials()

    # 从配置管理器获取服务器配置
    server_config = config_manager.get_server_config()
    performance_config = config_manager.get_performance_config()

    # 服务器配置（配置管理器已处理优先级：环境变量 > 配置文件 > 默认值）
    host = config_manager.get('host')
    port = config_manager.get('port')

    # 性能配置
    threads = config_manager.get('threads')
    connection_limit = config_manager.get('connection_limit')
    cleanup_interval = config_manager.get('cleanup_interval')
    channel_timeout = config_manager.get('channel_timeout')

    # 记录启动信息
    logger.info("="*60)
    logger.info("AI Hub服务器启动中...")
    logger.info(f"环境: {environment}")
    logger.info(f"监听地址: {host}:{port}")
    logger.info(f"工作线程: {threads}")
    logger.info(f"连接限制: {connection_limit}")
    logger.info(f"清理间隔: {cleanup_interval}s")
    logger.info(f"通道超时: {channel_timeout}s")
    logger.info(f"日志目录: {logging_config.get('log_dir', 'logs')}")
    logger.info(f"日志级别: {logging_config.get('log_level_name', 'INFO')}")
    logger.info(f"日志保留: {logging_config.get('backup_count', 30)}天")
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"工作目录: {os.getcwd()}")
    logger.info("="*60)

    try:
        # 创建Flask应用
        logger.info("正在初始化Flask应用...")
        app = create_app()
        logger.info("Flask应用初始化完成")

        # 使用Waitress作为WSGI服务器（适合生产环境）
        logger.info("正在启动Waitress WSGI服务器...")
        serve(
            app,
            host=host,
            port=port,
            threads=threads,
            connection_limit=connection_limit,
            cleanup_interval=cleanup_interval,
            channel_timeout=channel_timeout
        )
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务器...")
        logger.info("服务器已安全关闭")
    except Exception as e:
        logger.error(f"服务器启动失败: {str(e)}")
        logger.exception("详细错误信息:")
        sys.exit(1)


if __name__ == '__main__':
    main()