# 千义听悟音频转写服务器配置指南

本文档详细介绍了千义听悟音频转写服务器的配置系统，包括配置文件、环境变量和配置优先级。

## 配置概述

### 配置特性

- **多环境支持**: 支持 `default`、`development`、`production` 三种环境
- **配置优先级**: 环境变量 > 配置文件 > 默认值
- **配置文件搜索**: 自动搜索多个路径下的配置文件
- **安全配置**: 支持凭证文件权限控制和敏感信息脱敏
- **动态配置**: 支持运行时配置验证和错误处理

### 配置来源优先级

1. **环境变量** (最高优先级)
2. **配置文件** (中等优先级)
3. **默认值** (最低优先级)

## 环境配置

### 环境变量

通过 `TINGWU_ENVIRONMENT` 环境变量指定运行环境：

```bash
# 开发环境
export TINGWU_ENVIRONMENT=development

# 生产环境
export TINGWU_ENVIRONMENT=production

# 默认环境
export TINGWU_ENVIRONMENT=default
```

### 环境说明

| 环境 | 说明 | 适用场景 |
|------|------|----------|
| `default` | 默认环境 | 本地开发、测试 |
| `development` | 开发环境 | 开发调试 |
| `production` | 生产环境 | 线上部署 |

## 配置文件

### 配置文件位置

系统使用以下配置文件：

- `./config/credentials.conf` (项目级配置)

### 配置文件格式

配置文件使用 INI 格式，支持多个环境配置：

```ini
# 默认环境配置
[default]
app_key = your_default_app_key
tingwu_endpoint = tingwu.cn-beijing.aliyuncs.com
api_version = 2023-09-30

# 开发环境配置
[development]
app_key = your_dev_app_key
tingwu_endpoint = tingwu.cn-beijing.aliyuncs.com
log_level = DEBUG

# 生产环境配置
[production]
app_key = your_prod_app_key
tingwu_endpoint = tingwu.cn-beijing.aliyuncs.com
log_level = INFO
connection_pool_size = 20
```

### 配置文件权限

为了安全起见，建议设置配置文件权限：

```bash
# 设置文件权限为仅所有者可读写
chmod 600 /path/to/credentials.conf

# 设置文件所有者
chown user:group /path/to/credentials.conf
```

## 配置项说明

### 阿里云凭证配置

| 配置项 | 说明 | 默认值 | 示例 |
|--------|------|--------|---------|
| `app_key` | 阿里云应用密钥 | - | `your_app_key` |
| `access_key_id` | 阿里云访问密钥ID | - | `LTAI5t...` |
| `access_key_secret` | 阿里云访问密钥Secret | - | `abc123...` |
| `region_id` | 阿里云区域ID | `cn-beijing` | `cn-beijing` |

### API配置

| 配置项 | 说明 | 默认值 | 示例 |
|--------|------|--------|---------|
| `tingwu_endpoint` | 千义听悟API端点 | `tingwu.cn-beijing.aliyuncs.com` | - |
| `api_version` | API版本 | `2023-09-30` | - |
| `api_pathname` | API路径 | `/openapi/tingwu/v2/tasks` | - |
| `connect_timeout` | 连接超时时间(秒) | `30` | `60` |
| `read_timeout` | 读取超时时间(秒) | `60` | `120` |

### 服务器配置

| 配置项 | 说明 | 默认值 | 示例 |
|--------|------|--------|---------|
| `host` | 监听地址 | `0.0.0.0` | `127.0.0.1` |
| `port` | 监听端口 | `5579` | `8080` |
| `threads` | 工作线程数 | `4` | `8` |
| `connection_limit` | 连接限制 | `100` | `200` |

### 日志配置

| 配置项 | 说明 | 默认值 | 示例 |
|--------|------|--------|---------|
| `log_level` | 日志级别 | `INFO` | `DEBUG` |
| `log_dir` | 日志目录 | `logs` | `/var/log/tingwu` |
| `backup_count` | 日志保留天数 | `30` | `7` |

### 性能配置

| 配置项 | 说明 | 默认值 | 示例 |
|--------|------|--------|---------|
| `connection_pool_size` | 连接池大小 | `10` | `20` |
| `cleanup_interval` | 清理间隔(秒) | `30` | `60` |
| `channel_timeout` | 通道超时(秒) | `120` | `300` |

### 重试配置

| 配置项 | 说明 | 默认值 | 示例 |
|--------|------|--------|---------|
| `retry_enabled` | 是否启用重试 | `true` | `false` |
| `max_attempts` | 最大重试次数 | `3` | `5` |
| `backoff_multiplier` | 退避倍数 | `2.0` | `1.5` |

## 环境变量配置

### 服务器环境变量

```bash
# 环境配置
export TINGWU_ENVIRONMENT=production

# 服务器配置
export TINGWU_HOST=0.0.0.0
export TINGWU_PORT=5579
export TINGWU_THREADS=4

# 日志配置
export LOG_DIR=/var/log/tingwu-server
export LOG_LEVEL=INFO
export LOG_BACKUP_COUNT=30

# 阿里云凭证
export ALIBABA_CLOUD_ACCESS_KEY_ID=your_access_key_id
export ALIBABA_CLOUD_ACCESS_KEY_SECRET=your_access_key_secret
export ALIBABA_CLOUD_REGION_ID=cn-beijing
```

### Docker环境变量

在 `docker-compose.yml` 中配置：

```yaml
environment:
  - TINGWU_ENVIRONMENT=production
  - TINGWU_HOST=0.0.0.0
  - TINGWU_PORT=5579
  - LOG_DIR=/app/logs
  - LOG_LEVEL=INFO
```

## 配置验证

### 配置检查

系统启动时会自动验证配置：

- **必需配置检查**: 验证必需的配置项是否存在
- **格式验证**: 检查配置值的格式是否正确
- **范围验证**: 验证数值配置是否在合理范围内

### 配置错误处理

- **缺失配置**: 使用默认值并记录警告
- **无效配置**: 使用默认值并记录错误
- **严重错误**: 停止启动并输出错误信息

## 配置示例

### 开发环境配置

```ini
[development]
# 阿里云凭证
app_key = dev_app_key_here
access_key_id = dev_access_key_id
access_key_secret = dev_access_key_secret
region_id = cn-beijing

# API配置
tingwu_endpoint = tingwu.cn-beijing.aliyuncs.com
connect_timeout = 30
read_timeout = 60

# 日志配置
log_level = DEBUG
log_dir = logs
backup_count = 7

# 性能配置
connection_pool_size = 5
```

### 生产环境配置

```ini
[production]
# 阿里云凭证
app_key = prod_app_key_here
access_key_id = prod_access_key_id
access_key_secret = prod_access_key_secret
region_id = cn-beijing

# API配置
tingwu_endpoint = tingwu.cn-beijing.aliyuncs.com
connect_timeout = 60
read_timeout = 120

# 服务器配置
host = 0.0.0.0
port = 5579
threads = 8
connection_limit = 200

# 日志配置
log_level = INFO
log_dir = /var/log/tingwu-server
backup_count = 30

# 性能配置
connection_pool_size = 20
cleanup_interval = 60
channel_timeout = 300

# 重试配置
retry_enabled = true
max_attempts = 3
backoff_multiplier = 2.0
```

## 配置管理最佳实践

### 安全实践

1. **权限控制**: 设置配置文件权限为 600
2. **凭证分离**: 不要在代码中硬编码凭证
3. **环境隔离**: 不同环境使用不同的配置文件
4. **敏感信息**: 使用环境变量或加密存储敏感信息

### 部署实践

1. **配置验证**: 部署前验证配置文件的正确性
2. **备份配置**: 定期备份重要的配置文件
3. **版本控制**: 将配置模板纳入版本控制
4. **监控配置**: 监控配置变更和配置错误

### 维护实践

1. **文档更新**: 及时更新配置文档
2. **配置审计**: 定期审计配置的合理性
3. **性能调优**: 根据实际情况调整性能配置
4. **日志分析**: 通过日志分析配置的有效性

## 故障排除

### 常见配置问题

1. **配置文件未找到**
   - 检查配置文件路径
   - 确认文件权限
   - 查看日志中的搜索路径

2. **配置格式错误**
   - 检查INI格式语法
   - 确认节名称正确
   - 验证配置项名称

3. **环境变量未生效**
   - 确认环境变量名称
   - 检查变量值格式
   - 重启服务使变量生效

4. **权限问题**
   - 检查配置文件权限
   - 确认用户访问权限
   - 查看系统日志

### 调试配置

1. **启用调试日志**:
   ```bash
   export LOG_LEVEL=DEBUG
   ```

2. **查看配置信息**:
   ```bash
   # 查看应用日志中的配置信息
   tail -f /var/log/tingwu-server/tingwu-server-$(date +%Y-%m-%d).log | grep "配置"
   ```

3. **测试配置**:
   ```bash
   # 使用CLI工具测试配置
   python -m tingwu_server.cli_tool --test-connection
   ```

## 配置迁移

### 从环境变量迁移到配置文件

1. 创建配置文件
2. 将环境变量值复制到配置文件
3. 测试配置文件
4. 移除环境变量
5. 重启服务验证

### 配置文件版本升级

1. 备份当前配置
2. 查看新版本配置变更
3. 更新配置文件
4. 验证配置正确性
5. 重启服务应用新配置