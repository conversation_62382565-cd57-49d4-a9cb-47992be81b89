# 千义听悟服务器 - 日志功能详解

本文档详细介绍千义听悟音频转写服务器的日志功能，包括配置、使用和维护。

## 📋 目录

- [日志功能概述](#日志功能概述)
- [日志配置](#日志配置)
- [日志文件结构](#日志文件结构)
- [日志内容详解](#日志内容详解)
- [日志查看和分析](#日志查看和分析)
- [日志维护](#日志维护)
- [故障排除](#故障排除)

## 🔍 日志功能概述

千义听悟服务器内置了完善的日志系统，具有以下特性：

### 核心特性
- **按天分割**: 每天自动创建新的日志文件
- **自动清理**: 自动删除超过保留期的旧日志文件
- **多级别支持**: 支持DEBUG、INFO、WARNING、ERROR、CRITICAL五个级别
- **脱敏处理**: 自动对敏感信息进行脱敏处理
- **结构化记录**: 统一的日志格式，便于分析和监控
- **高性能**: 异步日志写入，不影响API响应性能

### 日志覆盖范围
- HTTP请求和响应
- API调用详情
- 系统启动和关闭
- 错误和异常信息
- 性能指标
- 安全事件

## ⚙️ 日志配置

### 环境变量配置

在`.env`文件中配置日志相关参数：

```bash
# 日志级别 (DEBUG/INFO/WARNING/ERROR/CRITICAL)
LOG_LEVEL=INFO

# 日志目录 (相对于应用根目录)
LOG_DIR=logs

# 日志文件保留天数
LOG_BACKUP_COUNT=30
```

### 配置说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `LOG_LEVEL` | `INFO` | 日志记录级别，只记录该级别及以上的日志 |
| `LOG_DIR` | `logs` | 日志文件存储目录 |
| `LOG_BACKUP_COUNT` | `30` | 保留的日志文件天数，超过的会被自动删除 |

### 日志级别说明

| 级别 | 数值 | 用途 | 示例 |
|------|------|------|------|
| `DEBUG` | 10 | 详细的调试信息 | 变量值、函数调用栈 |
| `INFO` | 20 | 一般信息记录 | 请求处理、任务完成 |
| `WARNING` | 30 | 警告信息 | 配置问题、性能警告 |
| `ERROR` | 40 | 错误信息 | API调用失败、异常处理 |
| `CRITICAL` | 50 | 严重错误 | 系统崩溃、服务不可用 |

## 📁 日志文件结构

### 文件命名规则

```
logs/
├── tingwu-server-2024-01-15.log    # 2024年1月15日的日志
├── tingwu-server-2024-01-16.log    # 2024年1月16日的日志
├── tingwu-server-2024-01-17.log    # 2024年1月17日的日志
└── ...
```

### 文件特点
- **按天分割**: 每天0点自动创建新文件
- **自动清理**: 超过`LOG_BACKUP_COUNT`天的文件会被自动删除
- **UTF-8编码**: 支持中文和特殊字符
- **追加模式**: 服务重启不会覆盖现有日志

## 📝 日志内容详解

### 日志格式

```
时间戳 - 记录器名称 - 级别 - 消息内容
```

示例：
```
2024-01-15 10:30:15,123 - tingwu_server - INFO - 服务器启动完成，监听地址: 0.0.0.0:5579
```

### 日志类型

#### 1. 系统启动日志
```
2024-01-15 10:30:10,001 - tingwu_server - INFO - 初始化日志系统，目录: /opt/tingwu-server/logs
2024-01-15 10:30:10,002 - tingwu_server - INFO - 日志级别: INFO, 保留天数: 30
2024-01-15 10:30:15,123 - tingwu_server - INFO - 服务器启动完成，监听地址: 0.0.0.0:5579
```

#### 2. HTTP请求日志
```
2024-01-15 10:30:25,456 - tingwu_server - INFO - HTTP请求开始 - POST /ai/aliyun/voice/tingwu/new
2024-01-15 10:30:25,457 - tingwu_server - INFO - 请求数据: {"taskKey": "task_123", "fileUrl": "http://***"}
2024-01-15 10:30:26,790 - tingwu_server - INFO - HTTP请求完成 - 200, 耗时: 1.334s
```

#### 3. API调用日志
```
2024-01-15 10:30:25,500 - tingwu_server - INFO - API调用开始 - 创建转写任务
2024-01-15 10:30:25,501 - tingwu_server - INFO - API参数: {"TaskKey": "task_123", "FileUrl": "http://***"}
2024-01-15 10:30:26,789 - tingwu_server - INFO - API调用成功，TaskId: abc123, 耗时: 1.289s
```

#### 4. 错误日志
```
2024-01-15 10:35:15,789 - tingwu_server - ERROR - API调用失败: InvalidParameter.FileUrl
2024-01-15 10:35:15,790 - tingwu_server - ERROR - 错误详情: 文件URL格式不正确
```

#### 5. CLI工具日志
```
2024-01-15 11:00:10,123 - tingwu_server - INFO - CLI工具初始化完成，AppKey: AK***
2024-01-15 11:00:15,456 - tingwu_server - INFO - CLI开始创建转写任务，TaskKey: cli_task_001
```

### 脱敏处理

为保护敏感信息，日志系统会自动对以下内容进行脱敏：

| 原始内容 | 脱敏后 | 说明 |
|----------|--------|------|
| `http://example.com/audio.mp3` | `http://***` | 文件URL |
| `LTAI5tABC123DEF456` | `LTAI***` | 阿里云AccessKey |
| `abc123def456ghi789` | `abc***` | 阿里云SecretKey |
| `task_20240115_user123` | `task_***` | 任务标识符 |

## 🔍 日志查看和分析

### 基本查看命令

```bash
# 查看今天的日志
tail -f logs/tingwu-server-$(date +%Y-%m-%d).log

# 查看最近100行
tail -n 100 logs/tingwu-server-$(date +%Y-%m-%d).log

# 查看所有日志文件
ls -la logs/

# 查看文件大小
du -h logs/
```

### 日志搜索

```bash
# 搜索错误日志
grep "ERROR" logs/tingwu-server-*.log

# 搜索特定任务
grep "task_123" logs/tingwu-server-*.log

# 搜索API调用
grep "API调用" logs/tingwu-server-*.log

# 搜索特定时间段
grep "2024-01-15 10:3" logs/tingwu-server-2024-01-15.log
```

### 日志分析

```bash
# 统计错误数量
grep -c "ERROR" logs/tingwu-server-*.log

# 统计API调用成功率
grep "API调用成功" logs/tingwu-server-*.log | wc -l
grep "API调用失败" logs/tingwu-server-*.log | wc -l

# 分析响应时间
grep "耗时" logs/tingwu-server-*.log | awk '{print $NF}'

# 查看最频繁的错误
grep "ERROR" logs/tingwu-server-*.log | sort | uniq -c | sort -nr
```

### 使用日志监控工具

#### 1. 使用journalctl (systemd)
```bash
# 查看服务日志
sudo journalctl -u tingwu-server -f

# 查看最近1小时的日志
sudo journalctl -u tingwu-server --since "1 hour ago"

# 查看特定时间段
sudo journalctl -u tingwu-server --since "2024-01-15 10:00" --until "2024-01-15 11:00"
```

#### 2. 使用logrotate管理
```bash
# 创建logrotate配置
sudo nano /etc/logrotate.d/tingwu-server

# 配置内容
/opt/tingwu-server/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    copytruncate
}
```

## 🧹 日志维护

### 自动清理

日志系统会自动执行以下清理操作：
- 每天检查日志文件
- 删除超过`LOG_BACKUP_COUNT`天的文件
- 清理空的日志文件

### 手动清理

```bash
# 清理7天前的日志
find logs/ -name "tingwu-server-*.log" -mtime +7 -delete

# 压缩旧日志文件
gzip logs/tingwu-server-$(date -d "yesterday" +%Y-%m-%d).log

# 查看磁盘使用情况
du -sh logs/
```

### 日志备份

```bash
# 创建备份脚本
#!/bin/bash
BACKUP_DIR="/backup/tingwu-logs"
SOURCE_DIR="/opt/tingwu-server/logs"
DATE=$(date +%Y%m%d)

mkdir -p "$BACKUP_DIR"
tar -czf "$BACKUP_DIR/tingwu-logs-$DATE.tar.gz" -C "$SOURCE_DIR" .

# 删除30天前的备份
find "$BACKUP_DIR" -name "tingwu-logs-*.tar.gz" -mtime +30 -delete
```

## 🔧 故障排除

### 常见问题

#### 1. 日志文件未生成

**可能原因：**
- 日志目录权限不足
- 磁盘空间不足
- 配置错误

**解决方法：**
```bash
# 检查目录权限
ls -la logs/

# 修复权限
sudo chown -R tingwu:tingwu logs/
sudo chmod 755 logs/

# 检查磁盘空间
df -h

# 检查配置
echo $LOG_DIR
echo $LOG_LEVEL
```

#### 2. 日志文件过大

**解决方法：**
```bash
# 检查文件大小
ls -lh logs/

# 压缩大文件
gzip logs/tingwu-server-*.log

# 调整保留天数
export LOG_BACKUP_COUNT=7
```

#### 3. 日志级别不正确

**解决方法：**
```bash
# 检查当前级别
grep "日志级别" logs/tingwu-server-$(date +%Y-%m-%d).log

# 修改级别
export LOG_LEVEL=DEBUG

# 重启服务
sudo systemctl restart tingwu-server
```

### 调试模式

启用调试模式获取更详细的日志：

```bash
# 临时启用调试模式
export LOG_LEVEL=DEBUG

# 重启服务
sudo systemctl restart tingwu-server

# 查看调试日志
tail -f logs/tingwu-server-$(date +%Y-%m-%d).log | grep DEBUG
```

### 性能监控

通过日志监控系统性能：

```bash
# 监控响应时间
grep "耗时" logs/tingwu-server-*.log | awk '{print $(NF-1)}' | sort -n

# 监控错误率
ERRORS=$(grep -c "ERROR" logs/tingwu-server-$(date +%Y-%m-%d).log)
TOTAL=$(grep -c "HTTP请求" logs/tingwu-server-$(date +%Y-%m-%d).log)
echo "错误率: $(echo "scale=2; $ERRORS/$TOTAL*100" | bc)%"

# 监控API调用频率
grep "API调用开始" logs/tingwu-server-$(date +%Y-%m-%d).log | wc -l
```

## 📊 日志最佳实践

### 1. 合理设置日志级别
- **开发环境**: DEBUG
- **测试环境**: INFO
- **生产环境**: WARNING或ERROR

### 2. 定期检查日志
- 每日检查错误日志
- 每周分析性能趋势
- 每月清理旧日志文件

### 3. 监控关键指标
- API调用成功率
- 平均响应时间
- 错误发生频率
- 系统资源使用

### 4. 建立告警机制
```bash
# 创建错误告警脚本
#!/bin/bash
ERROR_COUNT=$(grep -c "ERROR" logs/tingwu-server-$(date +%Y-%m-%d).log)
if [ $ERROR_COUNT -gt 10 ]; then
    echo "警告：今日错误数量超过阈值($ERROR_COUNT)" | mail -s "千义听悟服务器告警" <EMAIL>
fi
```

---

通过合理使用日志功能，您可以更好地监控和维护千义听悟音频转写服务器，及时发现和解决问题，确保服务的稳定运行。