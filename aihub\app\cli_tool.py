# -*- coding: utf-8 -*-
"""
千义听悟命令行工具

提供命令行接口，用于测试和调用千义听悟API进行音频转写。
"""

import json
import time
import argparse
from typing import Dict, Any
from .tingwu import TingwuClient
from .utils import extract_audio_info_from_url, format_file_size
from .logger import setup_logging, get_logger
from .config_manager import get_config_manager


class TingwuCLI:
    """
    千义听悟命令行工具类

    提供命令行接口来调用千义听悟API。
    """

    def __init__(self, app_key: str = None, environment: str = None):
        """
        初始化CLI工具

        Args:
            app_key (str): 阿里云应用密钥（可选，优先级高于配置文件）
            environment (str): 环境名称（default/development/production）
        """
        # 获取配置管理器
        self.config_manager = get_config_manager(environment)

        # 获取日志配置
        logging_config = self.config_manager.get_logging_config()

        # 初始化日志系统（CLI工具使用较短的保留期）
        cli_backup_count = min(logging_config.get('backup_count', 30), 7)  # CLI工具最多保留7天日志
        setup_logging(
            log_dir=logging_config.get('log_dir', 'logs'),
            log_level=logging_config.get('log_level', 20),
            backup_count=cli_backup_count
        )
        self.logger = get_logger()

        # 获取AppKey（参数优先级高于配置文件）
        final_app_key = app_key or self.config_manager.get_app_key()

        # 检查AppKey是否配置
        if not final_app_key:
            raise ValueError("AppKey未配置，请在配置文件中设置app_key或通过参数传入")

        # 创建客户端
        self.client = TingwuClient(app_key=final_app_key, environment=environment)

        # 记录初始化信息（脱敏处理）
        masked_app_key = final_app_key[:8] + '***' if len(final_app_key) > 8 else '***'
        self.logger.info(f"千义听悟CLI工具已初始化，AppKey: {masked_app_key}, Environment: {self.config_manager.environment}")

        # 记录配置信息
        config_info = self.config_manager.get_config_info()
        self.logger.info(f"配置来源: {config_info['config_sources']}, 配置文件: {config_info.get('config_file_path', 'None')}")

        print(f"千义听悟CLI工具已初始化")
        print(f"环境: {self.config_manager.environment}")
        print(f"AppKey: {masked_app_key}")
        if config_info.get('config_file_path'):
            print(f"配置文件: {config_info['config_file_path']}")

    def create_task(self, task_key: str, file_url: str, **kwargs) -> Dict[str, Any]:
        """
        创建转写任务

        Args:
            task_key (str): 任务唯一标识符
            file_url (str): 音频文件URL
            **kwargs: 其他可选参数

        Returns:
            Dict[str, Any]: API响应结果
        """
        self.logger.info(f"CLI开始创建转写任务，TaskKey: {task_key}")

        print(f"\n=== 创建转写任务 ===")
        print(f"任务标识: {task_key}")
        print(f"音频文件: {file_url}")

        # 提取音频文件信息
        audio_info = extract_audio_info_from_url(file_url)
        print(f"文件名: {audio_info['filename']}")
        print(f"文件格式: {audio_info['format']}")
        print(f"文件域名: {audio_info['domain']}")

        # 记录音频文件信息到日志
        self.logger.info(f"音频文件信息 - 文件名: {audio_info['filename']}, 格式: {audio_info['format']}, 域名: {audio_info['domain']}")

        # 显示配置参数
        config = {
            'source_language': kwargs.get('source_language', 'cn'),
            'sample_rate': kwargs.get('sample_rate', 16000),
            'speaker_count': kwargs.get('speaker_count', 0),
            'output_level': kwargs.get('output_level', 1),
            'diarization_enabled': kwargs.get('diarization_enabled', True),
            'progressive_callbacks_enabled': kwargs.get('progressive_callbacks_enabled', True),
            'translation_enabled': kwargs.get('translation_enabled', False),
            'auto_chapters_enabled': kwargs.get('auto_chapters_enabled', False),
            'meeting_assistance_enabled': kwargs.get('meeting_assistance_enabled', False),
            'summarization_enabled': kwargs.get('summarization_enabled', False)
        }

        print(f"\n配置参数:")
        for key, value in config.items():
            print(f"  {key}: {value}")

        # 记录配置参数到日志
        self.logger.info(f"CLI任务配置: {json.dumps(config, ensure_ascii=False)}")

        try:
            print(f"\n正在调用千义听悟API...")
            start_time = time.time()

            result = self.client.create_transcription_task(
                task_key=task_key,
                file_url=file_url,
                **config
            )

            end_time = time.time()
            response_time = end_time - start_time
            print(f"API调用完成，耗时: {response_time:.2f}秒")

            # 记录成功结果到日志
            self.logger.info(f"CLI任务创建成功，TaskKey: {task_key}, 耗时: {response_time:.3f}s")

            # 格式化输出结果
            self._print_result(result)

            return result

        except Exception as e:
            error_msg = f"任务创建失败: {str(e)}"
            self.logger.error(f"CLI任务创建失败，TaskKey: {task_key}, 错误: {error_msg}")
            print(f"❌ {error_msg}")
            raise

    def _print_result(self, result: Dict[str, Any]):
        """
        格式化打印API响应结果

        Args:
            result (Dict[str, Any]): API响应结果
        """
        print(f"\n=== API响应结果 ===")

        # 检查响应格式
        if 'Code' in result:
            code = result.get('Code', 'Unknown')
            message = result.get('Message', 'No message')
            request_id = result.get('RequestId', 'No request ID')

            if code == '0' or code == 0:
                print(f"✅ 任务创建成功")
            else:
                print(f"❌ 任务创建失败")

            print(f"响应码: {code}")
            print(f"响应消息: {message}")
            print(f"请求ID: {request_id}")

            # 打印任务数据
            data = result.get('Data', {})
            if data:
                print(f"\n任务信息:")
                task_id = data.get('TaskId', 'Unknown')
                task_key = data.get('TaskKey', 'Unknown')
                task_status = data.get('TaskStatus', 'Unknown')
                meeting_join_url = data.get('MeetingJoinUrl', '')

                print(f"  任务ID: {task_id}")
                print(f"  任务标识: {task_key}")
                print(f"  任务状态: {task_status}")

                if meeting_join_url:
                    print(f"  会议加入URL: {meeting_join_url}")

                # 状态说明
                status_descriptions = {
                    'ONGOING': '进行中',
                    'COMPLETED': '已完成',
                    'FAILED': '失败',
                    'PENDING': '等待中'
                }

                if task_status in status_descriptions:
                    print(f"  状态说明: {status_descriptions[task_status]}")

        # 打印完整JSON响应（用于调试）
        print(f"\n=== 完整JSON响应 ===")
        print(json.dumps(result, indent=2, ensure_ascii=False))

    def test_connection(self):
        """
        测试与千义听悟API的连接
        """
        self.logger.info("CLI开始测试API连接")
        print(f"\n=== 测试API连接 ===")

        # 使用示例参数测试连接
        test_task_key = f"test_connection_{int(time.time())}"
        test_file_url = "http://lcmp.zixushuili.com/assets/voice.mp4"

        self.logger.info(f"使用测试参数 - TaskKey: {test_task_key}, FileUrl: {test_file_url}")

        try:
            result = self.create_task(test_task_key, test_file_url)
            self.logger.info("CLI API连接测试成功")
            print(f"\n✅ API连接测试成功")
            return True
        except Exception as e:
            error_msg = f"API连接测试失败: {str(e)}"
            self.logger.error(f"CLI API连接测试失败: {error_msg}")
            print(f"\n❌ {error_msg}")
            return False


def create_task_interactive():
    """
    交互式创建转写任务
    """
    print("=== 千义听悟音频转写工具 ===")
    print("请输入以下信息创建转写任务:\n")

    # 获取用户输入
    task_key = input("任务标识 (TaskKey): ").strip()
    if not task_key:
        task_key = f"task_tingwu_{int(time.time())}"
        print(f"使用默认任务标识: {task_key}")

    file_url = input("音频文件URL: ").strip()
    if not file_url:
        file_url = "http://lcmp.zixushuili.com/assets/voice.mp4"
        print(f"使用默认音频文件: {file_url}")

    # 可选参数
    print("\n可选参数 (直接回车使用默认值):")

    source_language = input("源语言 [cn]: ").strip() or 'cn'
    sample_rate = input("采样率 [16000]: ").strip()
    sample_rate = int(sample_rate) if sample_rate.isdigit() else 16000

    speaker_count = input("说话人数量 [0-自动检测]: ").strip()
    speaker_count = int(speaker_count) if speaker_count.isdigit() else 0

    # 布尔参数
    diarization_enabled = input("启用说话人分离 [Y/n]: ").strip().lower() not in ['n', 'no', 'false']
    translation_enabled = input("启用翻译 [y/N]: ").strip().lower() in ['y', 'yes', 'true']

    # 创建CLI实例并执行任务
    cli = TingwuCLI()

    try:
        result = cli.create_task(
            task_key=task_key,
            file_url=file_url,
            source_language=source_language,
            sample_rate=sample_rate,
            speaker_count=speaker_count,
            diarization_enabled=diarization_enabled,
            translation_enabled=translation_enabled
        )

        print(f"\n🎉 任务创建完成！")

        # 提取任务ID用于后续查询
        if 'Data' in result and 'TaskId' in result['Data']:
            task_id = result['Data']['TaskId']
            print(f"\n💡 提示: 您可以使用任务ID '{task_id}' 查询转写进度和结果")

    except Exception as e:
        print(f"\n❌ 任务创建失败: {str(e)}")


def main():
    """
    命令行主函数
    """
    parser = argparse.ArgumentParser(
        description='千义听悟音频转写命令行工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 交互式创建任务
  python -m tingwu_server.cli_tool

  # 直接创建任务
  python -m tingwu_server.cli_tool --task-key my_task --file-url http://example.com/audio.mp3

  # 测试API连接
  python -m tingwu_server.cli_tool --test
        """
    )

    parser.add_argument('--task-key', help='任务唯一标识符')
    parser.add_argument('--file-url', help='音频文件的HTTP访问地址')
    parser.add_argument('--source-language', default='cn', help='源语言 (默认: cn)')
    parser.add_argument('--sample-rate', type=int, default=16000, help='采样率 (默认: 16000)')
    parser.add_argument('--speaker-count', type=int, default=0, help='说话人数量 (默认: 0-自动检测)')
    parser.add_argument('--app-key', help='阿里云应用密钥')
    parser.add_argument('--test', action='store_true', help='测试API连接')
    parser.add_argument('--no-diarization', action='store_true', help='禁用说话人分离')
    parser.add_argument('--enable-translation', action='store_true', help='启用翻译')

    args = parser.parse_args()

    # 创建CLI实例
    cli = TingwuCLI(args.app_key)

    if args.test:
        # 测试连接
        cli.test_connection()
    elif args.task_key and args.file_url:
        # 直接创建任务
        try:
            cli.create_task(
                task_key=args.task_key,
                file_url=args.file_url,
                source_language=args.source_language,
                sample_rate=args.sample_rate,
                speaker_count=args.speaker_count,
                diarization_enabled=not args.no_diarization,
                translation_enabled=args.enable_translation
            )
        except Exception as e:
            print(f"任务创建失败: {str(e)}")
            exit(1)
    else:
        # 交互式模式
        create_task_interactive()


if __name__ == '__main__':
    main()